Guide to Creating an Agent Hustle Telegram Bot
This guide outlines the steps to create a Telegram bot using the telegram-node-bot library and integrate it with the Emblem Vault Hustle Incognito AI Agent API using the hustle-incognito SDK.

1. Setting up the Development Environment
You will need Node.js installed.

Initialize your project:
Bash

npm init -y
Install necessary libraries:
Bash

npm install telegram-node-bot hustle-incognito dotenv
telegram-node-bot: For building the Telegram bot structure and handling updates.   
hustle-incognito: For interacting with the Agent Hustle AI API.   
dotenv: To manage environment variables like your API keys.
2. Obtaining API Tokens
Get a Telegram Bot Token: Talk to @BotFather on Telegram and create a new bot. You will receive an HTTP API token.   
Get an Emblem Vault Hustle Incognito API Key: Obtain your API key for the Agent Hustle API.
3. Structuring Your Bot
The telegram-node-bot library encourages an MVC-like structure with a router and controllers.   

Create your main bot file (e.g., app.js or app.ts if using TypeScript).

Initialize the Telegram bot with your token.   

Initialize the HustleIncognitoClient with your API key. It's recommended to use environment variables for sensitive keys.   

JavaScript

'use strict'
const Telegram = require('telegram-node-bot');
const { HustleIncognitoClient } = require('hustle-incognito');
require('dotenv').config();

const TelegramBaseController = Telegram.TelegramBaseController;
const TextCommand = Telegram.TextCommand;
const tg = new Telegram.Telegram(process.env.TELEGRAM_BOT_TOKEN);

const hustleClient = new HustleIncognitoClient({
  apiKey: process.env.HUSTLE_API_KEY,
  // Optional: configure the API URL if needed
  // hustleApiUrl: 'https://agenthustle.ai',
  debug: process.env.DEBUG === 'true' // Enable debug logging
});
Define controllers to handle specific commands or types of updates. Extend TelegramBaseController for messages, TelegramBaseCallbackQueryController for callback queries, or TelegramBaseInlineQueryController for inline queries.   

Set up the router to map commands to your controllers.   

4. Integrating the Agent Hustle API
Within your controllers, you can use the hustleClient instance to interact with the Agent Hustle API.

Create a controller to handle user messages that require AI processing.

Use hustleClient.chat() for a simple request/response interaction. This is suitable for commands where you wait for the complete AI response before replying.   

JavaScript

class HustleController extends TelegramBaseController {
  async handle($) {
    const userMessage = $.message.text;
    const chatId = $.chatId;
    const userId = $.userId; // Useful for vaultId or user-specific context

    // Send message to Agent Hustle API
    try {
      const response = await hustleClient.chat([
        { role: 'user', content: userMessage }
      ], {
        vaultId: `telegram-chat-${chatId}`, // Use a unique ID for the conversation context
        // externalWalletAddress: 'user_wallet_address', // Optional: if blockchain ops needed
      });

      // Send the AI's response back to the user
      $.sendMessage(response.content);

      // Log tool calls if any
      if (response.toolCalls && response.toolCalls.length > 0) {
          console.log('Tools used:', response.toolCalls); // [cite: 1746, 1793]
      }

    } catch (error) {
      console.error('Error interacting with Agent Hustle:', error);
      $.sendMessage('Sorry, I encountered an error processing your request.');
    }
  }
}

// Add the controller to your router
tg.router.when(new TextCommand('/hustle', 'handle'), new HustleController());
// Or use .any() to process all messages
// tg.router.any(new HustleController());
Use hustleClient.chatStream() for streaming responses, which is ideal for interactive UIs but can also be used to display the AI's response as it's generated. You can process different types of chunks received from the stream, such as text, tool calls, and tool results.   

JavaScript

class StreamingHustleController extends TelegramBaseController {
  async handle($) {
    const userMessage = $.message.text;
    const chatId = $.chatId;

    try {
      // Send message and stream the response
      const stream = hustleClient.chatStream({
        vaultId: `telegram-chat-${chatId}`,
        messages: [{ role: 'user', content: userMessage }],
        processChunks: true // Process raw chunks into structured data
      });

      let fullResponse = '';
      $.sendMessage("Agent thinking..."); // Initial message

      for await (const chunk of stream) { // [cite: 1730]
        if (chunk.type === 'text') { // [cite: 1731]
          fullResponse += chunk.value;
          // In a real bot, you might edit the message to show incremental updates
          // $.editMessageText(chatId, messageId, fullResponse);
        } else if (chunk.type === 'tool_call') { // [cite: 1734]
           console.log('Agent is using tool:', chunk.value.name); // [cite: 1747]
           // You could send a message indicating which tool is being used
           // $.sendMessage(`Agent is using the ${chunk.value.name} tool...`);
        } else if (chunk.type === 'tool_result') { // [cite: 1735]
           console.log('Tool result received for:', chunk.value.tool_call_id); // [cite: 1745]
           // You could log or process tool results here
        } else if (chunk.type === 'finish') { // [cite: 1732]
           console.log('Stream finished.');
           // Ensure the final response is sent if not using message editing
           if(fullResponse.length > 0) {
             $.sendMessage(`Agent: ${fullResponse}`);
           }
        }
      }

    } catch (error) {
      console.error('Error streaming from Agent Hustle:', error);
      $.sendMessage('Sorry, I encountered an error.');
    }
  }
}

// Add the streaming controller to your router
// tg.router.when(new TextCommand('/hustlestream', 'handle'), new StreamingHustleController());
5. Running Your Bot
Execute your main bot file using Node.js:

Bash

node app.js
Your bot should now be running and responsive to commands you've defined.