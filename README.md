# HustleBot 🤖

A Telegram bot integrated with Agent Hu<PERSON>le AI, providing intelligent conversation and assistance capabilities.

## Features ✨

- Interactive chat with Agent <PERSON> AI
- Command-based interaction system
- Real-time streaming responses
- Message deduplication
- Robust error handling
- Secure environment configuration

## Available Commands 📝

- `/start` - Initialize the bot and see welcome message
- `/help` - Display all available commands
- `/hustle [message]` - Chat with Agent Hu<PERSON>le AI
- `/hustlestream [message]` - Chat with streaming responses

## Prerequisites 📋

Before running the bot, make sure you have:

- Node.js (v16 or higher)
- npm or yarn package manager
- Telegram Bot <PERSON>ken (from [@BotFather](https://t.me/botfather))
- Agent Hustle API Key

## Installation 🚀

1. Clone the repository:
   ```bash
   git clone [your-repository-url]
   cd hustlebot
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory:
   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   HUSTLE_API_KEY=your_hustle_api_key
   ```

## Running the Bot 🏃‍♂️

Start the bot with:
```bash
npm start
```

The bot will confirm successful connection to both Telegram and Agent Hu<PERSON>le services in the console.

## Project Structure 📁

```
hustlebot/
├── app.js              # Main application file
├── .env                # Environment variables
├── package.json        # Project dependencies
├── BUILD.md           # Build instructions
├── ROADMAP.md         # Project roadmap
└── AgentHustleGiude.md # Agent Hustle integration guide
```

## Environment Variables ⚙️

Required environment variables:
- `TELEGRAM_BOT_TOKEN`: Your Telegram bot token
- `HUSTLE_API_KEY`: Your Agent Hustle API key

## Error Handling 🛠️

The bot includes comprehensive error handling for:
- Missing environment variables
- API connection issues
- Message processing errors
- Authentication failures

## Development 👨‍💻

For detailed development instructions and guidelines, please refer to:
- `BUILD.md` for build instructions
- `ROADMAP.md` for project roadmap
- `AgentHustleGiude.md` for Agent Hustle integration details

## Dependencies 📦

- `node-telegram-bot-api`: Telegram Bot API integration
- `hustle-incognito`: Agent Hustle AI client
- `dotenv`: Environment configuration

## License 📄

ISC License

## Contributing 🤝

Contributions are welcome! Please read our contributing guidelines before submitting pull requests.

## Support 💬

For support, please open an issue in the repository or contact the maintainers.

---

Made with ❤️ using Node.js and Agent Hustle AI 