{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Telegram bot integrated with Agent Hustle AI", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cronstrue": "^2.61.0", "dotenv": "^16.5.0", "hustle-incognito": "^0.1.3", "node-cron": "^4.0.5", "node-telegram-bot-api": "^0.66.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "eslint": "^9.26.0", "jest": "^29.7.0", "nodemon": "^3.1.10"}}